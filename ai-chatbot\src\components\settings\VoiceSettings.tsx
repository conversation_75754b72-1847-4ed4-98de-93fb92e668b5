'use client';

import { useState, useEffect } from 'react';
import { Volume2, VolumeX, Mic, MicOff } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface VoiceSettingsProps {
  voiceEnabled: boolean;
  onVoiceEnabledChange: (enabled: boolean) => void;
}

export default function VoiceSettings({ voiceEnabled, onVoiceEnabledChange }: VoiceSettingsProps) {
  const [speechSupported, setSpeechSupported] = useState(false);
  const [recognitionSupported, setRecognitionSupported] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>('');

  useEffect(() => {
    // Check for speech synthesis support
    if ('speechSynthesis' in window) {
      setSpeechSupported(true);
      
      // Load voices
      const loadVoices = () => {
        const availableVoices = window.speechSynthesis.getVoices();
        setVoices(availableVoices);
        
        // Set default voice (first English voice or first available)
        const englishVoice = availableVoices.find(voice => voice.lang.startsWith('en'));
        if (englishVoice) {
          setSelectedVoice(englishVoice.name);
        } else if (availableVoices.length > 0) {
          setSelectedVoice(availableVoices[0].name);
        }
      };

      // Load voices immediately and on voiceschanged event
      loadVoices();
      window.speechSynthesis.onvoiceschanged = loadVoices;
    }

    // Check for speech recognition support
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setRecognitionSupported(true);
    }
  }, []);

  const testVoice = () => {
    if (!speechSupported) {
      toast.error('Speech synthesis not supported');
      return;
    }

    const utterance = new SpeechSynthesisUtterance('Hello! This is how I sound.');
    
    if (selectedVoice) {
      const voice = voices.find(v => v.name === selectedVoice);
      if (voice) {
        utterance.voice = voice;
      }
    }

    utterance.onstart = () => toast.success('Testing voice...');
    utterance.onerror = () => toast.error('Voice test failed');
    
    window.speechSynthesis.speak(utterance);
  };

  const testMicrophone = async () => {
    if (!recognitionSupported) {
      toast.error('Speech recognition not supported');
      return;
    }

    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });
      toast.success('Microphone access granted');
    } catch (error) {
      toast.error('Microphone access denied');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Voice Features
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Configure voice input and output settings
          </p>
        </div>
        <button
          onClick={() => onVoiceEnabledChange(!voiceEnabled)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            voiceEnabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              voiceEnabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {voiceEnabled && (
        <div className="space-y-4 pl-4 border-l-2 border-blue-200 dark:border-blue-800">
          {/* Speech Synthesis Settings */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Volume2 size={16} className="text-blue-600" />
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                Text-to-Speech
              </h4>
              {speechSupported ? (
                <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded">
                  Supported
                </span>
              ) : (
                <span className="text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded">
                  Not Supported
                </span>
              )}
            </div>

            {speechSupported && (
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Voice
                  </label>
                  <select
                    value={selectedVoice}
                    onChange={(e) => setSelectedVoice(e.target.value)}
                    className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    {voices.map((voice) => (
                      <option key={voice.name} value={voice.name}>
                        {voice.name} ({voice.lang})
                      </option>
                    ))}
                  </select>
                </div>

                <button
                  onClick={testVoice}
                  className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                >
                  <Volume2 size={14} />
                  Test Voice
                </button>
              </div>
            )}
          </div>

          {/* Speech Recognition Settings */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Mic size={16} className="text-green-600" />
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                Speech Recognition
              </h4>
              {recognitionSupported ? (
                <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded">
                  Supported
                </span>
              ) : (
                <span className="text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded">
                  Not Supported
                </span>
              )}
            </div>

            {recognitionSupported && (
              <button
                onClick={testMicrophone}
                className="flex items-center gap-2 px-3 py-2 text-sm bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
              >
                <Mic size={14} />
                Test Microphone
              </button>
            )}
          </div>

          {/* Browser Compatibility Info */}
          <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
            <p className="font-medium mb-1">Browser Compatibility:</p>
            <ul className="space-y-1">
              <li>• Text-to-Speech: Chrome, Firefox, Safari, Edge</li>
              <li>• Speech Recognition: Chrome, Edge (Webkit-based browsers)</li>
              <li>• Microphone permission required for speech input</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
