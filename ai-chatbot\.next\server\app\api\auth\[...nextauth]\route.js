"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_GPT_ai_chatbot_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_GPT_ai_chatbot_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDUTtBQUV6QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jaGF0Ym90Ly4vc3JjL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzPzAwOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gJ25leHQtYXV0aCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgnO1xuXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xuXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUIH07XG4iXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJhdXRoT3B0aW9ucyIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                },\n                name: {\n                    label: \"Name\",\n                    type: \"text\"\n                },\n                isSignUp: {\n                    label: \"Is Sign Up\",\n                    type: \"text\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Email and password are required\");\n                }\n                const isSignUp = credentials.isSignUp === \"true\";\n                if (isSignUp) {\n                    // Sign up logic\n                    if (!credentials.name) {\n                        throw new Error(\"Name is required for sign up\");\n                    }\n                    // Check if user already exists\n                    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.UserService.findUserByEmail(credentials.email);\n                    if (existingUser) {\n                        throw new Error(\"User already exists with this email\");\n                    }\n                    // Create new user\n                    const newUser = await _database__WEBPACK_IMPORTED_MODULE_2__.UserService.createUser({\n                        email: credentials.email,\n                        name: credentials.name,\n                        password: credentials.password,\n                        provider: \"credentials\",\n                        preferences: {\n                            theme: \"light\",\n                            language: \"en\",\n                            tone: \"friendly\",\n                            voiceEnabled: false\n                        }\n                    });\n                    return {\n                        id: newUser._id.toString(),\n                        email: newUser.email,\n                        name: newUser.name,\n                        image: newUser.image\n                    };\n                } else {\n                    // Sign in logic\n                    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.UserService.findUserByEmail(credentials.email);\n                    if (!user || !user.password) {\n                        throw new Error(\"Invalid credentials\");\n                    }\n                    const isValidPassword = await _database__WEBPACK_IMPORTED_MODULE_2__.UserService.verifyPassword(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        throw new Error(\"Invalid credentials\");\n                    }\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        image: user.image\n                    };\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            if (account?.provider === \"google\") {\n                try {\n                    // Check if user exists\n                    let existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.UserService.findUserByEmail(user.email);\n                    if (!existingUser) {\n                        // Create new user from Google OAuth\n                        existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.UserService.createUser({\n                            email: user.email,\n                            name: user.name,\n                            image: user.image,\n                            provider: \"google\",\n                            preferences: {\n                                theme: \"light\",\n                                language: \"en\",\n                                tone: \"friendly\",\n                                voiceEnabled: false\n                            }\n                        });\n                    }\n                    user.id = existingUser._id.toString();\n                    return true;\n                } catch (error) {\n                    console.error(\"Error during Google sign in:\", error);\n                    return false;\n                }\n            }\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\"\n    },\n    session: {\n        strategy: \"jwt\"\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatSessionService: () => (/* binding */ ChatSessionService),\n/* harmony export */   MessageService: () => (/* binding */ MessageService),\n/* harmony export */   UserService: () => (/* binding */ UserService)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./models */ \"(rsc)/./src/lib/models.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\n\n// User operations\nclass UserService {\n    static async createUser(userData) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const now = new Date();\n        const user = {\n            ...userData,\n            preferences: {\n                ..._models__WEBPACK_IMPORTED_MODULE_2__.defaultUserPreferences,\n                ...userData.preferences\n            },\n            createdAt: now,\n            updatedAt: now\n        };\n        if (user.password) {\n            user.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"].hash(user.password, 12);\n        }\n        const result = await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.USERS).insertOne(user);\n        return {\n            ...user,\n            _id: result.insertedId\n        };\n    }\n    static async findUserByEmail(email) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        return await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.USERS).findOne({\n            email\n        });\n    }\n    static async findUserById(id) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof id === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(id) : id;\n        return await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.USERS).findOne({\n            _id: objectId\n        });\n    }\n    static async updateUserPreferences(userId, preferences) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof userId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(userId) : userId;\n        await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.USERS).updateOne({\n            _id: objectId\n        }, {\n            $set: {\n                preferences,\n                updatedAt: new Date()\n            }\n        });\n    }\n    static async verifyPassword(plainPassword, hashedPassword) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"].compare(plainPassword, hashedPassword);\n    }\n}\n// Chat Session operations\nclass ChatSessionService {\n    static async createSession(userId, title = \"New Chat\") {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const now = new Date();\n        const objectId = typeof userId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(userId) : userId;\n        const session = {\n            userId: objectId,\n            title,\n            createdAt: now,\n            updatedAt: now,\n            messageCount: 0\n        };\n        const result = await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CHAT_SESSIONS).insertOne(session);\n        return {\n            ...session,\n            _id: result.insertedId\n        };\n    }\n    static async getUserSessions(userId) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof userId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(userId) : userId;\n        return await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CHAT_SESSIONS).find({\n            userId: objectId\n        }).sort({\n            updatedAt: -1\n        }).toArray();\n    }\n    static async getSessionById(sessionId) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof sessionId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(sessionId) : sessionId;\n        return await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CHAT_SESSIONS).findOne({\n            _id: objectId\n        });\n    }\n    static async updateSessionTitle(sessionId, title) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof sessionId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(sessionId) : sessionId;\n        await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CHAT_SESSIONS).updateOne({\n            _id: objectId\n        }, {\n            $set: {\n                title,\n                updatedAt: new Date()\n            }\n        });\n    }\n    static async deleteSession(sessionId) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof sessionId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(sessionId) : sessionId;\n        // Delete session and all its messages\n        await Promise.all([\n            db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CHAT_SESSIONS).deleteOne({\n                _id: objectId\n            }),\n            db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.MESSAGES).deleteMany({\n                sessionId: objectId\n            })\n        ]);\n    }\n}\n// Message operations\nclass MessageService {\n    static async addMessage(messageData) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const now = new Date();\n        const message = {\n            ...messageData,\n            timestamp: now\n        };\n        const result = await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.MESSAGES).insertOne(message);\n        // Update session message count and timestamp\n        await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CHAT_SESSIONS).updateOne({\n            _id: messageData.sessionId\n        }, {\n            $inc: {\n                messageCount: 1\n            },\n            $set: {\n                updatedAt: now\n            }\n        });\n        return {\n            ...message,\n            _id: result.insertedId\n        };\n    }\n    static async getSessionMessages(sessionId) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof sessionId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(sessionId) : sessionId;\n        return await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.MESSAGES).find({\n            sessionId: objectId\n        }).sort({\n            timestamp: 1\n        }).toArray();\n    }\n    static async getRecentMessages(sessionId, limit = 10) {\n        const db = await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const objectId = typeof sessionId === \"string\" ? new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(sessionId) : sessionId;\n        return await db.collection(_models__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.MESSAGES).find({\n            sessionId: objectId\n        }).sort({\n            timestamp: -1\n        }).limit(limit).toArray();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models.ts":
/*!***************************!*\
  !*** ./src/lib/models.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   defaultUserPreferences: () => (/* binding */ defaultUserPreferences)\n/* harmony export */ });\n// Default user preferences\nconst defaultUserPreferences = {\n    theme: \"light\",\n    language: \"en\",\n    tone: \"friendly\",\n    voiceEnabled: false\n};\n// Collection names\nconst COLLECTIONS = {\n    USERS: \"users\",\n    CHAT_SESSIONS: \"chatSessions\",\n    MESSAGES: \"messages\",\n    ANALYTICS: \"analytics\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nif (!process.env.MONGODB_URI) {\n    throw new Error('Invalid/Missing environment variable: \"MONGODB_URI\"');\n}\nconst uri = process.env.MONGODB_URI;\nconst options = {};\nlet client;\nlet clientPromise;\nif (true) {\n    // In development mode, use a global variable so that the value\n    // is preserved across module reloads caused by HMR (Hot Module Replacement).\n    let globalWithMongo = global;\n    if (!globalWithMongo._mongoClientPromise) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        globalWithMongo._mongoClientPromise = client.connect();\n    }\n    clientPromise = globalWithMongo._mongoClientPromise;\n} else {}\n// Export a module-scoped MongoClient promise. By doing this in a\n// separate module, the client can be shared across functions.\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n// Helper function to get database\nasync function getDatabase() {\n    const client = await clientPromise;\n    return client.db(\"ai-chatbot\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/@panva","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/object-hash","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();