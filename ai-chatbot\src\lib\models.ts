import { ObjectId } from 'mongodb';

// User interface
export interface User {
  _id?: ObjectId;
  email: string;
  name: string;
  password?: string; // Optional for OAuth users
  provider: 'credentials' | 'google';
  image?: string;
  preferences: {
    theme: 'light' | 'dark';
    language: 'en' | 'es' | 'fr';
    tone: 'friendly' | 'professional' | 'sarcastic';
    voiceEnabled: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Chat Session interface
export interface ChatSession {
  _id?: ObjectId;
  userId: ObjectId;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

// Message interface
export interface Message {
  _id?: ObjectId;
  sessionId: ObjectId;
  userId: ObjectId;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: {
    tone?: string;
    language?: string;
    voiceUsed?: boolean;
    apiResponse?: any;
  };
}

// Analytics interface for admin dashboard
export interface Analytics {
  _id?: ObjectId;
  date: Date;
  totalUsers: number;
  totalSessions: number;
  totalMessages: number;
  activeUsers: number;
  averageSessionLength: number;
  popularTones: Record<string, number>;
  popularLanguages: Record<string, number>;
}

// Default user preferences
export const defaultUserPreferences = {
  theme: 'light' as const,
  language: 'en' as const,
  tone: 'friendly' as const,
  voiceEnabled: false,
};

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  CHAT_SESSIONS: 'chatSessions',
  MESSAGES: 'messages',
  ANALYTICS: 'analytics',
} as const;
