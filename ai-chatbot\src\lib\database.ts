import { ObjectId } from 'mongodb';
import { getDatabase } from './mongodb';
import { User, ChatSession, Message, Analytics, COLLECTIONS, defaultUserPreferences } from './models';
import bcrypt from 'bcryptjs';

// User operations
export class UserService {
  static async createUser(userData: Omit<User, '_id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const db = await getDatabase();
    const now = new Date();
    
    const user: User = {
      ...userData,
      preferences: { ...defaultUserPreferences, ...userData.preferences },
      createdAt: now,
      updatedAt: now,
    };

    if (user.password) {
      user.password = await bcrypt.hash(user.password, 12);
    }

    const result = await db.collection<User>(COLLECTIONS.USERS).insertOne(user);
    return { ...user, _id: result.insertedId };
  }

  static async findUserByEmail(email: string): Promise<User | null> {
    const db = await getDatabase();
    return await db.collection<User>(COLLECTIONS.USERS).findOne({ email });
  }

  static async findUserById(id: string | ObjectId): Promise<User | null> {
    const db = await getDatabase();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    return await db.collection<User>(COLLECTIONS.USERS).findOne({ _id: objectId });
  }

  static async updateUserPreferences(userId: string | ObjectId, preferences: Partial<User['preferences']>): Promise<void> {
    const db = await getDatabase();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    await db.collection<User>(COLLECTIONS.USERS).updateOne(
      { _id: objectId },
      { 
        $set: { 
          preferences,
          updatedAt: new Date()
        }
      }
    );
  }

  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }
}

// Chat Session operations
export class ChatSessionService {
  static async createSession(userId: string | ObjectId, title: string = 'New Chat'): Promise<ChatSession> {
    const db = await getDatabase();
    const now = new Date();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const session: ChatSession = {
      userId: objectId,
      title,
      createdAt: now,
      updatedAt: now,
      messageCount: 0,
    };

    const result = await db.collection<ChatSession>(COLLECTIONS.CHAT_SESSIONS).insertOne(session);
    return { ...session, _id: result.insertedId };
  }

  static async getUserSessions(userId: string | ObjectId): Promise<ChatSession[]> {
    const db = await getDatabase();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    return await db.collection<ChatSession>(COLLECTIONS.CHAT_SESSIONS)
      .find({ userId: objectId })
      .sort({ updatedAt: -1 })
      .toArray();
  }

  static async getSessionById(sessionId: string | ObjectId): Promise<ChatSession | null> {
    const db = await getDatabase();
    const objectId = typeof sessionId === 'string' ? new ObjectId(sessionId) : sessionId;
    
    return await db.collection<ChatSession>(COLLECTIONS.CHAT_SESSIONS).findOne({ _id: objectId });
  }

  static async updateSessionTitle(sessionId: string | ObjectId, title: string): Promise<void> {
    const db = await getDatabase();
    const objectId = typeof sessionId === 'string' ? new ObjectId(sessionId) : sessionId;
    
    await db.collection<ChatSession>(COLLECTIONS.CHAT_SESSIONS).updateOne(
      { _id: objectId },
      { 
        $set: { 
          title,
          updatedAt: new Date()
        }
      }
    );
  }

  static async deleteSession(sessionId: string | ObjectId): Promise<void> {
    const db = await getDatabase();
    const objectId = typeof sessionId === 'string' ? new ObjectId(sessionId) : sessionId;
    
    // Delete session and all its messages
    await Promise.all([
      db.collection(COLLECTIONS.CHAT_SESSIONS).deleteOne({ _id: objectId }),
      db.collection(COLLECTIONS.MESSAGES).deleteMany({ sessionId: objectId })
    ]);
  }
}

// Message operations
export class MessageService {
  static async addMessage(messageData: Omit<Message, '_id' | 'timestamp'>): Promise<Message> {
    const db = await getDatabase();
    const now = new Date();
    
    const message: Message = {
      ...messageData,
      timestamp: now,
    };

    const result = await db.collection<Message>(COLLECTIONS.MESSAGES).insertOne(message);
    
    // Update session message count and timestamp
    await db.collection<ChatSession>(COLLECTIONS.CHAT_SESSIONS).updateOne(
      { _id: messageData.sessionId },
      { 
        $inc: { messageCount: 1 },
        $set: { updatedAt: now }
      }
    );

    return { ...message, _id: result.insertedId };
  }

  static async getSessionMessages(sessionId: string | ObjectId): Promise<Message[]> {
    const db = await getDatabase();
    const objectId = typeof sessionId === 'string' ? new ObjectId(sessionId) : sessionId;
    
    return await db.collection<Message>(COLLECTIONS.MESSAGES)
      .find({ sessionId: objectId })
      .sort({ timestamp: 1 })
      .toArray();
  }

  static async getRecentMessages(sessionId: string | ObjectId, limit: number = 10): Promise<Message[]> {
    const db = await getDatabase();
    const objectId = typeof sessionId === 'string' ? new ObjectId(sessionId) : sessionId;
    
    return await db.collection<Message>(COLLECTIONS.MESSAGES)
      .find({ sessionId: objectId })
      .sort({ timestamp: -1 })
      .limit(limit)
      .toArray();
  }
}
