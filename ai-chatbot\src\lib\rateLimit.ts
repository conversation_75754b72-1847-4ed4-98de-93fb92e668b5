import { getDatabase } from './mongodb';

interface RateLimitEntry {
  userId: string;
  requests: number;
  resetTime: Date;
}

export class RateLimiter {
  private static readonly COLLECTION_NAME = 'rateLimits';
  private static readonly MAX_REQUESTS_PER_HOUR = 100;
  private static readonly WINDOW_SIZE_MS = 60 * 60 * 1000; // 1 hour

  static async checkRateLimit(userId: string): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    const db = await getDatabase();
    const collection = db.collection<RateLimitEntry>(this.COLLECTION_NAME);
    
    const now = new Date();
    const windowStart = new Date(now.getTime() - this.WINDOW_SIZE_MS);
    
    // Find or create rate limit entry
    let entry = await collection.findOne({ userId });
    
    if (!entry || entry.resetTime < windowStart) {
      // Create new entry or reset expired entry
      entry = {
        userId,
        requests: 0,
        resetTime: new Date(now.getTime() + this.WINDOW_SIZE_MS),
      };
      
      await collection.replaceOne(
        { userId },
        entry,
        { upsert: true }
      );
    }
    
    // Check if limit exceeded
    if (entry.requests >= this.MAX_REQUESTS_PER_HOUR) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
      };
    }
    
    // Increment request count
    await collection.updateOne(
      { userId },
      { $inc: { requests: 1 } }
    );
    
    return {
      allowed: true,
      remaining: this.MAX_REQUESTS_PER_HOUR - entry.requests - 1,
      resetTime: entry.resetTime,
    };
  }
  
  static async getRemainingRequests(userId: string): Promise<number> {
    const db = await getDatabase();
    const collection = db.collection<RateLimitEntry>(this.COLLECTION_NAME);
    
    const now = new Date();
    const windowStart = new Date(now.getTime() - this.WINDOW_SIZE_MS);
    
    const entry = await collection.findOne({ userId });
    
    if (!entry || entry.resetTime < windowStart) {
      return this.MAX_REQUESTS_PER_HOUR;
    }
    
    return Math.max(0, this.MAX_REQUESTS_PER_HOUR - entry.requests);
  }
  
  static async resetUserLimit(userId: string): Promise<void> {
    const db = await getDatabase();
    const collection = db.collection<RateLimitEntry>(this.COLLECTION_NAME);
    
    await collection.deleteOne({ userId });
  }
  
  // Clean up expired entries (should be called periodically)
  static async cleanupExpiredEntries(): Promise<void> {
    const db = await getDatabase();
    const collection = db.collection<RateLimitEntry>(this.COLLECTION_NAME);
    
    const now = new Date();
    const windowStart = new Date(now.getTime() - this.WINDOW_SIZE_MS);
    
    await collection.deleteMany({ resetTime: { $lt: windowStart } });
  }
}
