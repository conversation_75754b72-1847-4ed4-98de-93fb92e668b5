import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import { UserService } from './database';
import { User } from './models';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        name: { label: 'Name', type: 'text' },
        isSignUp: { label: 'Is Sign Up', type: 'text' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password are required');
        }

        const isSignUp = credentials.isSignUp === 'true';

        if (isSignUp) {
          // Sign up logic
          if (!credentials.name) {
            throw new Error('Name is required for sign up');
          }

          // Check if user already exists
          const existingUser = await UserService.findUserByEmail(credentials.email);
          if (existingUser) {
            throw new Error('User already exists with this email');
          }

          // Create new user
          const newUser = await UserService.createUser({
            email: credentials.email,
            name: credentials.name,
            password: credentials.password,
            provider: 'credentials',
            preferences: {
              theme: 'light',
              language: 'en',
              tone: 'friendly',
              voiceEnabled: false,
            },
          });

          return {
            id: newUser._id!.toString(),
            email: newUser.email,
            name: newUser.name,
            image: newUser.image,
          };
        } else {
          // Sign in logic
          const user = await UserService.findUserByEmail(credentials.email);
          if (!user || !user.password) {
            throw new Error('Invalid credentials');
          }

          const isValidPassword = await UserService.verifyPassword(credentials.password, user.password);
          if (!isValidPassword) {
            throw new Error('Invalid credentials');
          }

          return {
            id: user._id!.toString(),
            email: user.email,
            name: user.name,
            image: user.image,
          };
        }
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          // Check if user exists
          let existingUser = await UserService.findUserByEmail(user.email!);
          
          if (!existingUser) {
            // Create new user from Google OAuth
            existingUser = await UserService.createUser({
              email: user.email!,
              name: user.name!,
              image: user.image,
              provider: 'google',
              preferences: {
                theme: 'light',
                language: 'en',
                tone: 'friendly',
                voiceEnabled: false,
              },
            });
          }
          
          user.id = existingUser._id!.toString();
          return true;
        } catch (error) {
          console.error('Error during Google sign in:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET,
};
