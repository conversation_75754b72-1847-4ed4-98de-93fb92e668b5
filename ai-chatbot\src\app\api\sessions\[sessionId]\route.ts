import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ChatSessionService, MessageService } from '@/lib/database';

// GET /api/sessions/[sessionId] - Get session with messages
export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { sessionId } = params;
    
    // Get session and verify ownership
    const chatSession = await ChatSessionService.getSessionById(sessionId);
    if (!chatSession || chatSession.userId.toString() !== session.user.id) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // Get messages
    const messages = await MessageService.getSessionMessages(sessionId);

    return NextResponse.json({
      session: chatSession,
      messages,
    });
  } catch (error) {
    console.error('Get session API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/sessions/[sessionId] - Update session title
export async function PUT(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { sessionId } = params;
    const { title } = await request.json();

    if (!title || typeof title !== 'string') {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 });
    }

    // Verify session ownership
    const chatSession = await ChatSessionService.getSessionById(sessionId);
    if (!chatSession || chatSession.userId.toString() !== session.user.id) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    await ChatSessionService.updateSessionTitle(sessionId, title);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Update session API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/sessions/[sessionId] - Delete session
export async function DELETE(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { sessionId } = params;

    // Verify session ownership
    const chatSession = await ChatSessionService.getSessionById(sessionId);
    if (!chatSession || chatSession.userId.toString() !== session.user.id) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    await ChatSessionService.deleteSession(sessionId);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Delete session API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
