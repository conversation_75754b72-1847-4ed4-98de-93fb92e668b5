import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ChatSessionService, MessageService } from '@/lib/database';

// GET /api/sessions - Get user's chat sessions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessions = await ChatSessionService.getUserSessions(session.user.id);
    return NextResponse.json({ sessions });
  } catch (error) {
    console.error('Sessions API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/sessions - Create new chat session
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { title } = await request.json();
    const newSession = await ChatSessionService.createSession(session.user.id, title);
    
    return NextResponse.json({ session: newSession });
  } catch (error) {
    console.error('Create session API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
