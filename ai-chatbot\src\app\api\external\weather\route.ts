import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { WeatherService } from '@/lib/externalApis';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const location = searchParams.get('location');
    const lat = searchParams.get('lat');
    const lon = searchParams.get('lon');

    let weatherData;

    if (lat && lon) {
      // Get weather by coordinates
      weatherData = await WeatherService.getWeatherByCoords(
        parseFloat(lat),
        parseFloat(lon)
      );
    } else if (location) {
      // Get weather by location name
      weatherData = await WeatherService.getCurrentWeather(location);
    } else {
      return NextResponse.json(
        { error: 'Location or coordinates required' },
        { status: 400 }
      );
    }

    if (!weatherData) {
      return NextResponse.json(
        { error: 'Weather data not available' },
        { status: 404 }
      );
    }

    return NextResponse.json({ weather: weatherData });
  } catch (error) {
    console.error('Weather API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
