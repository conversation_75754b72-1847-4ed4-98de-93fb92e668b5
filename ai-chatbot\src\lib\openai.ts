import OpenAI from 'openai';

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable');
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatOptions {
  tone: 'friendly' | 'professional' | 'sarcastic';
  language: 'en' | 'es' | 'fr';
  maxTokens?: number;
  temperature?: number;
}

// System prompts for different tones
const SYSTEM_PROMPTS = {
  friendly: "You are a friendly and helpful AI assistant. Be warm, conversational, and supportive in your responses. Use a casual tone and show empathy when appropriate.",
  professional: "You are a professional AI assistant. Provide clear, concise, and well-structured responses. Maintain a formal but approachable tone and focus on being informative and helpful.",
  sarcastic: "You are a witty and slightly sarcastic AI assistant. Use humor and light sarcasm in your responses, but remain helpful and informative. Don't be mean or offensive, just playfully sarcastic.",
};

// Language instructions
const LANGUAGE_INSTRUCTIONS = {
  en: "Respond in English.",
  es: "Responde en español.",
  fr: "Répondez en français.",
};

export class OpenAIService {
  static async generateResponse(
    messages: ChatMessage[],
    options: ChatOptions
  ): Promise<string> {
    try {
      // Create system message based on tone and language
      const systemMessage: ChatMessage = {
        role: 'system',
        content: `${SYSTEM_PROMPTS[options.tone]} ${LANGUAGE_INSTRUCTIONS[options.language]}`,
      };

      // Prepare messages for OpenAI
      const openaiMessages = [systemMessage, ...messages];

      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: openaiMessages,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7,
        stream: false,
      });

      return completion.choices[0]?.message?.content || 'I apologize, but I could not generate a response.';
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  static async generateStreamResponse(
    messages: ChatMessage[],
    options: ChatOptions
  ): Promise<ReadableStream> {
    try {
      // Create system message based on tone and language
      const systemMessage: ChatMessage = {
        role: 'system',
        content: `${SYSTEM_PROMPTS[options.tone]} ${LANGUAGE_INSTRUCTIONS[options.language]}`,
      };

      // Prepare messages for OpenAI
      const openaiMessages = [systemMessage, ...messages];

      const stream = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: openaiMessages,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7,
        stream: true,
      });

      // Create a readable stream
      return new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of stream) {
              const content = chunk.choices[0]?.delta?.content || '';
              if (content) {
                controller.enqueue(new TextEncoder().encode(content));
              }
            }
            controller.close();
          } catch (error) {
            controller.error(error);
          }
        },
      });
    } catch (error) {
      console.error('OpenAI streaming error:', error);
      throw new Error('Failed to generate streaming AI response');
    }
  }

  static async generateTitle(messages: ChatMessage[]): Promise<string> {
    try {
      // Use the first few messages to generate a title
      const relevantMessages = messages.slice(0, 4);
      
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'Generate a short, descriptive title (max 6 words) for this conversation based on the messages. Do not use quotes or special characters.',
          },
          ...relevantMessages,
        ],
        max_tokens: 20,
        temperature: 0.3,
      });

      return completion.choices[0]?.message?.content?.trim() || 'New Chat';
    } catch (error) {
      console.error('Title generation error:', error);
      return 'New Chat';
    }
  }
}
