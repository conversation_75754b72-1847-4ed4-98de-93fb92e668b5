import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { OpenAIService, ChatMessage } from '@/lib/openai';
import { MessageService, ChatSessionService, UserService } from '@/lib/database';
import { RateLimiter } from '@/lib/rateLimit';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;

    // Check rate limit
    const rateLimitResult = await RateLimiter.checkRateLimit(userId);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          resetTime: rateLimitResult.resetTime 
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimitResult.resetTime.toISOString(),
          }
        }
      );
    }

    // Parse request body
    const { message, sessionId, stream = false } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    // Get user preferences
    const user = await UserService.findUserById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get or create chat session
    let session_id = sessionId;
    if (!session_id) {
      const newSession = await ChatSessionService.createSession(userId);
      session_id = newSession._id!.toString();
    }

    // Get recent messages for context
    const recentMessages = await MessageService.getRecentMessages(session_id, 10);
    
    // Convert to OpenAI format (reverse order for chronological)
    const contextMessages: ChatMessage[] = recentMessages
      .reverse()
      .map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      }));

    // Add current user message
    contextMessages.push({
      role: 'user',
      content: message,
    });

    // Save user message to database
    await MessageService.addMessage({
      sessionId: session_id,
      userId,
      role: 'user',
      content: message,
      metadata: {
        tone: user.preferences.tone,
        language: user.preferences.language,
      },
    });

    // Generate AI response
    const chatOptions = {
      tone: user.preferences.tone,
      language: user.preferences.language,
    };

    if (stream) {
      // Streaming response
      const responseStream = await OpenAIService.generateStreamResponse(contextMessages, chatOptions);
      
      // We need to collect the full response to save to database
      let fullResponse = '';
      const transformStream = new TransformStream({
        transform(chunk, controller) {
          const text = new TextDecoder().decode(chunk);
          fullResponse += text;
          controller.enqueue(chunk);
        },
        flush() {
          // Save assistant message to database
          MessageService.addMessage({
            sessionId: session_id,
            userId,
            role: 'assistant',
            content: fullResponse,
            metadata: {
              tone: user.preferences.tone,
              language: user.preferences.language,
            },
          });
        },
      });

      return new Response(responseStream.pipeThrough(transformStream), {
        headers: {
          'Content-Type': 'text/plain',
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
        },
      });
    } else {
      // Non-streaming response
      const aiResponse = await OpenAIService.generateResponse(contextMessages, chatOptions);

      // Save assistant message to database
      await MessageService.addMessage({
        sessionId: session_id,
        userId,
        role: 'assistant',
        content: aiResponse,
        metadata: {
          tone: user.preferences.tone,
          language: user.preferences.language,
        },
      });

      // Generate title for new sessions
      if (!sessionId && contextMessages.length <= 2) {
        const title = await OpenAIService.generateTitle(contextMessages);
        await ChatSessionService.updateSessionTitle(session_id, title);
      }

      return NextResponse.json({
        response: aiResponse,
        sessionId: session_id,
      }, {
        headers: {
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
        },
      });
    }
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
