{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../src/core/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAA;AACvD,OAAO,KAAK,EACV,QAAQ,EACR,eAAe,EACf,YAAY,EAOb,MAAM,cAAc,CAAA;AACrB,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAA;AACvD,OAAO,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AAC7C,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAA;AACrD,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,QAAQ,CAAA;AAMpD,oBAAY,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;AAE7C,YAAY,EAAE,cAAc,EAAE,CAAA;AAE9B;;;;GAIG;AACH,MAAM,WAAW,WAAW;IAC1B;;;;;;;;OAQG;IACH,SAAS,EAAE,QAAQ,EAAE,CAAA;IACrB;;;;;;;;;;OAUG;IACH,MAAM,CAAC,EAAE,MAAM,CAAA;IACf;;;;;;;OAOG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IACjC;;;;;;;OAOG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,CAAA;IACzB;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,CAAA;IAC7B;;;;;;;;OAQG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAA;IACrC;;;;;;;;;;;OAWG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IAChC;;;;;;;OAOG;IACH,OAAO,CAAC,EAAE,OAAO,CAAA;IACjB;;;;;;;;OAQG;IACH,KAAK,CAAC,EAAE,OAAO,CAAA;IACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IAChC;;;;;;;;;OASG;IACH,KAAK,CAAC,EAAE,KAAK,CAAA;IACb;;;;;;;;;;;;;OAaG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAA;IAC1B;;;;;;;;;;;;;;;OAeG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;CAClC;AAED;;;;;GAKG;AACH,MAAM,WAAW,KAAK;IACpB,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;IACvC,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,UAAU,CAAC,EAAE,MAAM,CAAA;CACpB;AAED;;;;GAIG;AACH,oBAAY,QAAQ,GAAG,kBAAkB,CAAA;AAEzC;;;GAGG;AACH,MAAM,WAAW,OAAQ,SAAQ,OAAO,CAAC,QAAQ,CAAC;IAChD;;;;;OAKG;IACH,iBAAiB,EAAE,MAAM,CAAA;IACzB,8CAA8C;IAC9C,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,+CAA+C;IAC/C,QAAQ,EAAE,MAAM,CAAA;IAChB,uCAAuC;IACvC,IAAI,EAAE,YAAY,CAAA;CACnB;AAED,oDAAoD;AACpD,MAAM,WAAW,OAAO;IACtB,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,KAAK,CAAC,EAAE,MAAM,CAAA;CACf;AAED,wEAAwE;AACxE,MAAM,WAAW,gBAAgB,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO;IACxD;;;;;;OAMG;IACH,MAAM,EAAE,CAAC,MAAM,EAAE;QACf,IAAI,EAAE,IAAI,GAAG,WAAW,CAAA;QACxB,OAAO,EAAE,CAAC,GAAG,IAAI,CAAA;QACjB;;;WAGG;QACH,OAAO,CAAC,EAAE,CAAC,CAAA;QACX;;;;;;;WAOG;QACH,KAAK,CAAC,EAAE;YACN,mBAAmB,CAAC,EAAE,OAAO,CAAA;SAC9B,CAAA;QACD,wEAAwE;QACxE,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,CAAA;KAC9C,KAAK,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA;IACjC;;;;;;OAMG;IACH,QAAQ,EAAE,CAAC,MAAM,EAAE;QACjB,iDAAiD;QACjD,GAAG,EAAE,MAAM,CAAA;QACX,yDAAyD;QACzD,OAAO,EAAE,MAAM,CAAA;KAChB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA;IACvB;;;;;;;;;;;;;;;OAeG;IACH,OAAO,EAAE,CACP,MAAM,EACF;QACE,OAAO,EAAE,OAAO,CAAA;QAChB,uEAAuE;QACvE,KAAK,EAAE,GAAG,CAAA;QACV,6EAA6E;QAC7E,IAAI,EAAE,WAAW,CAAA;KAClB,GAAG;QACF;;;;;WAKG;QACH,UAAU,EAAE,GAAG,CAAA;QACf,OAAO,EAAE,QAAQ,CAAA;KAClB,KACF,SAAS,CAAC,OAAO,GAAG,cAAc,CAAC,CAAA;IACxC;;;;;;;;;;;OAWG;IACH,GAAG,EAAE,CAEH,MAAM,EAAE;QACN;;;;;WAKG;QACH,KAAK,EAAE,GAAG,CAAA;QACV;;;;;;;WAOG;QACH,IAAI,EAAE,IAAI,GAAG,WAAW,CAAA;QACxB;;;;WAIG;QACH,OAAO,EAAE,CAAC,GAAG,IAAI,CAAA;QACjB;;;;WAIG;QACH,OAAO,CAAC,EAAE,CAAC,CAAA;QACX;;;;;;WAMG;QACH,OAAO,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAA;QACxC,qDAAqD;QACrD,SAAS,CAAC,EAAE,OAAO,CAAA;QACnB;;;;;WAKG;QACH,OAAO,CAAC,EAAE,GAAG,CAAA;KACd,KACE,SAAS,CAAC,GAAG,CAAC,CAAA;CACpB;AAED,8EAA8E;AAC9E,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE,sBAAsB,CAAA;CAChC;AAED,8EAA8E;AAC9E,MAAM,WAAW,cAAc;IAC7B,YAAY,EAAE,YAAY,CAAA;IAC1B,WAAW,EAAE,YAAY,CAAA;IACzB,SAAS,EAAE,YAAY,CAAA;IACvB,gBAAgB,EAAE,YAAY,CAAA;IAC9B,KAAK,EAAE,YAAY,CAAA;IACnB,KAAK,EAAE,YAAY,CAAA;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,cAAc;IAC7B;;;;;OAKG;IACH,MAAM,EAAE,CAAC,OAAO,EAAE;QAChB,IAAI,EAAE,IAAI,CAAA;QACV,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;QACvB,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,SAAS,CAAC,EAAE,OAAO,CAAA;KACpB,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IACrB;;;;;OAKG;IACH,OAAO,EAAE,CAAC,OAAO,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,EAAE,GAAG,CAAA;KAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IACvE,UAAU,EAAE,CAAC,OAAO,EAAE;QAAE,IAAI,EAAE,IAAI,CAAA;KAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IACxD,UAAU,EAAE,CAAC,OAAO,EAAE;QAAE,IAAI,EAAE,IAAI,CAAA;KAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IACxD,WAAW,EAAE,CAAC,OAAO,EAAE;QACrB,IAAI,EAAE,IAAI,GAAG,WAAW,CAAA;QACxB,OAAO,EAAE,OAAO,CAAA;QAChB,OAAO,EAAE,IAAI,GAAG,WAAW,CAAA;KAC5B,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IACrB;;;;;OAKG;IACH,OAAO,EAAE,CAAC,OAAO,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,EAAE,GAAG,CAAA;KAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;CACxE;AAED,oBAAY,SAAS,GAAG,MAAM,cAAc,CAAA;AAE5C,oEAAoE;AACpE,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,MAAM,CAAA;IACf,mDAAmD;IACnD,KAAK,EAAE,MAAM,CAAA;IACb,aAAa,EAAE,MAAM,CAAA;IACrB,+DAA+D;IAC/D,OAAO,EAAE,MAAM,CAAA;CAChB;AAED,oBAAY,aAAa,GAAG,MAAM,CAAA;AAElC,MAAM,WAAW,cAAc;IAC7B,IAAI,CAAC,EAAE;QACL,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACpB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACrB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;KACtB,CAAA;IACD,OAAO,EAAE,aAAa,CAAA;CACvB;AAED;;;;;;;;GAQG;AACH,MAAM,WAAW,OAAQ,SAAQ,cAAc;CAAG;AAElD,oBAAY,eAAe,GAAG,KAAK,GAAG,UAAU,CAAA;AAEhD,8EAA8E;AAC9E,MAAM,WAAW,cAAc;IAC7B;;;;;;;;;;;OAWG;IACH,QAAQ,EAAE,eAAe,CAAA;IACzB;;;OAGG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;;;OAIG;IACH,SAAS,EAAE,MAAM,CAAA;IACjB;;;;;OAKG;IACH,oBAAoB,EAAE,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA;CAC9C;AAED,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAA;IACV,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACpB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACrB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;CACtB;AAED;;;;;;;;;GASG;AACH,MAAM,WAAW,IAAK,SAAQ,WAAW;CAAG;AAwB5C,oBAAY,UAAU,GAClB,WAAW,GACX,SAAS,GACT,MAAM,GACN,QAAQ,GACR,SAAS,GACT,UAAU,GACV,gBAAgB,GAChB,OAAO,GACP,MAAM,CAAA"}