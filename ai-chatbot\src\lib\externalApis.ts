// External API integrations for weather, news, and dictionary

export interface WeatherData {
  location: string;
  temperature: number;
  description: string;
  humidity: number;
  windSpeed: number;
  icon: string;
}

export interface NewsArticle {
  title: string;
  description: string;
  url: string;
  source: string;
  publishedAt: string;
  urlToImage?: string;
}

export interface DictionaryEntry {
  word: string;
  phonetic?: string;
  meanings: {
    partOfSpeech: string;
    definitions: {
      definition: string;
      example?: string;
      synonyms?: string[];
    }[];
  }[];
}

export class WeatherService {
  private static readonly API_KEY = process.env.WEATHER_API_KEY;
  private static readonly BASE_URL = 'https://api.openweathermap.org/data/2.5';

  static async getCurrentWeather(location: string): Promise<WeatherData | null> {
    if (!this.API_KEY) {
      console.warn('Weather API key not configured');
      return null;
    }

    try {
      const response = await fetch(
        `${this.BASE_URL}/weather?q=${encodeURIComponent(location)}&appid=${this.API_KEY}&units=metric`
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status}`);
      }

      const data = await response.json();

      return {
        location: data.name,
        temperature: Math.round(data.main.temp),
        description: data.weather[0].description,
        humidity: data.main.humidity,
        windSpeed: data.wind.speed,
        icon: data.weather[0].icon,
      };
    } catch (error) {
      console.error('Weather API error:', error);
      return null;
    }
  }

  static async getWeatherByCoords(lat: number, lon: number): Promise<WeatherData | null> {
    if (!this.API_KEY) {
      console.warn('Weather API key not configured');
      return null;
    }

    try {
      const response = await fetch(
        `${this.BASE_URL}/weather?lat=${lat}&lon=${lon}&appid=${this.API_KEY}&units=metric`
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status}`);
      }

      const data = await response.json();

      return {
        location: data.name,
        temperature: Math.round(data.main.temp),
        description: data.weather[0].description,
        humidity: data.main.humidity,
        windSpeed: data.wind.speed,
        icon: data.weather[0].icon,
      };
    } catch (error) {
      console.error('Weather API error:', error);
      return null;
    }
  }
}

export class NewsService {
  private static readonly API_KEY = process.env.NEWS_API_KEY;
  private static readonly BASE_URL = 'https://newsapi.org/v2';

  static async getTopHeadlines(country: string = 'us', category?: string): Promise<NewsArticle[]> {
    if (!this.API_KEY) {
      console.warn('News API key not configured');
      return [];
    }

    try {
      const params = new URLSearchParams({
        country,
        apiKey: this.API_KEY,
        pageSize: '5',
      });

      if (category) {
        params.append('category', category);
      }

      const response = await fetch(`${this.BASE_URL}/top-headlines?${params}`);

      if (!response.ok) {
        throw new Error(`News API error: ${response.status}`);
      }

      const data = await response.json();

      return data.articles.map((article: any) => ({
        title: article.title,
        description: article.description,
        url: article.url,
        source: article.source.name,
        publishedAt: article.publishedAt,
        urlToImage: article.urlToImage,
      }));
    } catch (error) {
      console.error('News API error:', error);
      return [];
    }
  }

  static async searchNews(query: string): Promise<NewsArticle[]> {
    if (!this.API_KEY) {
      console.warn('News API key not configured');
      return [];
    }

    try {
      const params = new URLSearchParams({
        q: query,
        apiKey: this.API_KEY,
        pageSize: '5',
        sortBy: 'relevancy',
      });

      const response = await fetch(`${this.BASE_URL}/everything?${params}`);

      if (!response.ok) {
        throw new Error(`News API error: ${response.status}`);
      }

      const data = await response.json();

      return data.articles.map((article: any) => ({
        title: article.title,
        description: article.description,
        url: article.url,
        source: article.source.name,
        publishedAt: article.publishedAt,
        urlToImage: article.urlToImage,
      }));
    } catch (error) {
      console.error('News API error:', error);
      return [];
    }
  }
}

export class DictionaryService {
  private static readonly BASE_URL = 'https://api.dictionaryapi.dev/api/v2/entries/en';

  static async lookupWord(word: string): Promise<DictionaryEntry | null> {
    try {
      const response = await fetch(`${this.BASE_URL}/${encodeURIComponent(word)}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null; // Word not found
        }
        throw new Error(`Dictionary API error: ${response.status}`);
      }

      const data = await response.json();
      const entry = data[0]; // Take the first entry

      return {
        word: entry.word,
        phonetic: entry.phonetic,
        meanings: entry.meanings.map((meaning: any) => ({
          partOfSpeech: meaning.partOfSpeech,
          definitions: meaning.definitions.map((def: any) => ({
            definition: def.definition,
            example: def.example,
            synonyms: def.synonyms,
          })),
        })),
      };
    } catch (error) {
      console.error('Dictionary API error:', error);
      return null;
    }
  }
}

// Helper function to detect and handle API requests in chat messages
export class ApiDetector {
  static detectWeatherRequest(message: string): string | null {
    const weatherPatterns = [
      /weather\s+in\s+([a-zA-Z\s,]+)/i,
      /what'?s\s+the\s+weather\s+like\s+in\s+([a-zA-Z\s,]+)/i,
      /temperature\s+in\s+([a-zA-Z\s,]+)/i,
      /how'?s\s+the\s+weather\s+in\s+([a-zA-Z\s,]+)/i,
    ];

    for (const pattern of weatherPatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    // Check for current location weather
    if (/weather|temperature/i.test(message) && /current|here|my location/i.test(message)) {
      return 'current_location';
    }

    return null;
  }

  static detectNewsRequest(message: string): { query?: string; category?: string } | null {
    const newsPatterns = [
      /news\s+about\s+([a-zA-Z\s]+)/i,
      /latest\s+news\s+on\s+([a-zA-Z\s]+)/i,
      /what'?s\s+happening\s+with\s+([a-zA-Z\s]+)/i,
    ];

    for (const pattern of newsPatterns) {
      const match = message.match(pattern);
      if (match) {
        return { query: match[1].trim() };
      }
    }

    // Check for general news request
    if (/news|headlines|current events/i.test(message)) {
      // Check for category
      const categories = ['business', 'entertainment', 'health', 'science', 'sports', 'technology'];
      for (const category of categories) {
        if (new RegExp(category, 'i').test(message)) {
          return { category };
        }
      }
      return {}; // General news
    }

    return null;
  }

  static detectDictionaryRequest(message: string): string | null {
    const dictionaryPatterns = [
      /define\s+([a-zA-Z]+)/i,
      /definition\s+of\s+([a-zA-Z]+)/i,
      /what\s+does\s+([a-zA-Z]+)\s+mean/i,
      /meaning\s+of\s+([a-zA-Z]+)/i,
    ];

    for (const pattern of dictionaryPatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return null;
  }
}
