import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UserService } from '@/lib/database';

// GET /api/user/preferences - Get user preferences
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await UserService.findUserById(session.user.id);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ preferences: user.preferences });
  } catch (error) {
    console.error('Get preferences API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/user/preferences - Update user preferences
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const preferences = await request.json();

    // Validate preferences
    const validThemes = ['light', 'dark'];
    const validLanguages = ['en', 'es', 'fr'];
    const validTones = ['friendly', 'professional', 'sarcastic'];

    if (preferences.theme && !validThemes.includes(preferences.theme)) {
      return NextResponse.json({ error: 'Invalid theme' }, { status: 400 });
    }

    if (preferences.language && !validLanguages.includes(preferences.language)) {
      return NextResponse.json({ error: 'Invalid language' }, { status: 400 });
    }

    if (preferences.tone && !validTones.includes(preferences.tone)) {
      return NextResponse.json({ error: 'Invalid tone' }, { status: 400 });
    }

    if (preferences.voiceEnabled !== undefined && typeof preferences.voiceEnabled !== 'boolean') {
      return NextResponse.json({ error: 'Invalid voiceEnabled value' }, { status: 400 });
    }

    await UserService.updateUserPreferences(session.user.id, preferences);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Update preferences API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
